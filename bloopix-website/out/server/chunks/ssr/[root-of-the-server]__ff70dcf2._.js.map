{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/PROJECTS/Bloopix%20MemeCoin/bloopix-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { useState, useEffect } from 'react';\n\nexport default function Hero() {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    setIsVisible(true);\n  }, []);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Animation */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 opacity-90\">\n        <div className=\"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%224%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] animate-pulse\"></div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"absolute top-0 left-0 right-0 z-50 p-6\">\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <Image\n              src=\"/bloopix-logo.jpeg\"\n              alt=\"Bloopix Logo\"\n              width={50}\n              height={50}\n              className=\"rounded-full border-2 border-white/20\"\n            />\n            <span className=\"text-white text-2xl font-bold\">Bloopix</span>\n          </div>\n          \n          <div className=\"hidden md:flex items-center space-x-8\">\n            <a href=\"#about\" className=\"text-white/90 hover:text-white transition-colors\">About</a>\n            <a href=\"#tokenomics\" className=\"text-white/90 hover:text-white transition-colors\">Tokenomics</a>\n            <a href=\"#roadmap\" className=\"text-white/90 hover:text-white transition-colors\">Roadmap</a>\n            <a \n              href=\"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-white text-purple-600 px-6 py-2 rounded-full font-semibold hover:bg-gray-100 transition-colors\"\n            >\n              Buy $BLP\n            </a>\n          </div>\n        </div>\n      </nav>\n\n      {/* Hero Content */}\n      <div className=\"relative z-10 text-center px-6 max-w-5xl mx-auto\">\n        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\n          <div className=\"mb-8\">\n            <Image\n              src=\"/bloopix-logo.jpeg\"\n              alt=\"Bloopix Logo\"\n              width={200}\n              height={200}\n              className=\"mx-auto rounded-full border-4 border-white/30 shadow-2xl\"\n            />\n          </div>\n          \n          <h1 className=\"text-6xl md:text-8xl font-bold text-white mb-6 leading-tight\">\n            Bloopix\n            <span className=\"block text-4xl md:text-5xl text-blue-200 font-normal mt-2\">\n              $BLP\n            </span>\n          </h1>\n          \n          <p className=\"text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed\">\n            From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token \n            designed for degens who don&apos;t take themselves too seriously but know how to make waves.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <a\n              href=\"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg\"\n            >\n              🚀 Buy $BLP on Pump.fun\n            </a>\n            \n            <a\n              href=\"https://x.com/bloopix11/status/1947960764603785465?s=46\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-105\"\n            >\n              🐦 Follow on X\n            </a>\n          </div>\n          \n          <div className=\"mt-12 text-white/80\">\n            <p className=\"text-sm mb-2\">Contract Address:</p>\n            <code className=\"bg-black/20 px-4 py-2 rounded-lg text-sm font-mono break-all\">\n              9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG\n            </code>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Bubbles Animation */}\n      <div className=\"absolute inset-0 pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute w-4 h-4 bg-white/20 rounded-full animate-bounce\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 2}s`,\n              animationDuration: `${2 + Math.random() * 2}s`,\n            }}\n          />\n        ))}\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce\">\n        <div className=\"flex flex-col items-center\">\n          <span className=\"text-sm mb-2\">Scroll to explore</span>\n          <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n          </svg>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;sCAGlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAmD;;;;;;8CAC9E,8OAAC;oCAAE,MAAK;oCAAc,WAAU;8CAAmD;;;;;;8CACnF,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAmD;;;;;;8CAChF,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAC,6BAA6B,EAAE,YAAY,8BAA8B,4BAA4B;;sCACpH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAG,WAAU;;gCAA+D;8CAE3E,8OAAC;oCAAK,WAAU;8CAA4D;;;;;;;;;;;;sCAK9E,8OAAC;4BAAE,WAAU;sCAA2E;;;;;;sCAKxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;8CACX;;;;;;8CAID,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAe;;;;;;8CAC5B,8OAAC;oCAAK,WAAU;8CAA+D;;;;;;;;;;;;;;;;;;;;;;;0BAQrF,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4BACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wBAChD;uBAPK;;;;;;;;;;0BAaX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAe;;;;;;sCAC/B,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACjE,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/PROJECTS/Bloopix%20MemeCoin/bloopix-website/src/components/About.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\n\nexport default function About() {\n  const [isVisible, setIsVisible] = useState(false);\n  const sectionRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.1 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const features = [\n    {\n      icon: '🌊',\n      title: 'Community-Driven',\n      description: 'Built by degens, for degens. No VCs, no team tokens, just pure community vibes.'\n    },\n    {\n      icon: '🎯',\n      title: 'Fair Launch',\n      description: '100% fair launch on Pumpfun. Everyone starts at the same price, no pre-sales or allocations.'\n    },\n    {\n      icon: '🚀',\n      title: 'Meme Power',\n      description: 'Powered by memes, viral content, and the collective energy of the Bloopix community.'\n    },\n    {\n      icon: '💎',\n      title: 'Diamond Hands',\n      description: 'For holders who believe in the power of fun, community, and making waves in crypto.'\n    }\n  ];\n\n  return (\n    <section id=\"about\" ref={sectionRef} className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n              About Bloopix\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Bloopix is here to remind everyone that crypto doesn&apos;t always have to be serious.\n              Amid the charts, candles, and anxiety-inducing volatility, Bloopix splashes onto \n              the scene to deliver joy and a light-hearted reminder of what brought many of us here.\n            </p>\n          </div>\n\n          {/* Vision & Mission */}\n          <div className=\"grid md:grid-cols-2 gap-12 mb-20\">\n            <div className=\"bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-2xl\">\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">🎯 Our Vision</h3>\n              <p className=\"text-gray-700 text-lg leading-relaxed\">\n                Bloopix is here to remind everyone that crypto doesn&apos;t always have to be serious.\n                Amid the charts, candles, and anxiety-inducing volatility, Bloopix splashes onto \n                the scene to deliver joy and a light-hearted reminder of what brought many of us \n                here in the first place: curiosity, community, and a little chaos.\n              </p>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 p-8 rounded-2xl\">\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">🚀 Our Mission</h3>\n              <ul className=\"text-gray-700 text-lg space-y-3\">\n                <li>• Inject humor and positivity into the Pumpfun scene</li>\n                <li>• Build a vibrant, self-aware community of degens and dreamers</li>\n                <li>• Ride the waves of Pumpfun while creating memorable experiences</li>\n                <li>• Show that fun coins can still make big splashes</li>\n              </ul>\n            </div>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n            {features.map((feature, index) => (\n              <div\n                key={index}\n                className={`bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 ${\n                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n                }`}\n                style={{ transitionDelay: `${index * 100}ms` }}\n              >\n                <div className=\"text-4xl mb-4\">{feature.icon}</div>\n                <h4 className=\"text-xl font-bold text-gray-900 mb-3\">{feature.title}</h4>\n                <p className=\"text-gray-600 leading-relaxed\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n\n          {/* Why Bloopix */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-white\">\n            <h3 className=\"text-4xl font-bold mb-8 text-center\">Why Bloopix? 🤔</h3>\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">😊</div>\n                <h4 className=\"font-semibold mb-2\">Cute & Relatable</h4>\n                <p className=\"text-blue-100 text-sm\">A happy, bubbly character designed to resonate with everyone</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">🧠</div>\n                <h4 className=\"font-semibold mb-2\">Memorable Branding</h4>\n                <p className=\"text-blue-100 text-sm\">Easy to recognize and share across all platforms</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">👥</div>\n                <h4 className=\"font-semibold mb-2\">Community-Oriented</h4>\n                <p className=\"text-blue-100 text-sm\">What matters is the people who hold it, share it, and spread the joy</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">😌</div>\n                <h4 className=\"font-semibold mb-2\">No Pressure</h4>\n                <p className=\"text-blue-100 text-sm\">No overblown roadmap or unrealistic promises - just good vibes</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,WAAW,OAAO,EAAE;YACtB,SAAS,OAAO,CAAC,WAAW,OAAO;QACrC;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAQ,KAAK;QAAY,WAAU;kBAC7C,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,YAAY,8BAA8B,4BAA4B;;kCAEpH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAQzE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAQvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAMV,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gCAEC,WAAW,CAAC,oIAAoI,EAC9I,YAAY,8BAA8B,4BAC1C;gCACF,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;;kDAE7C,8OAAC;wCAAI,WAAU;kDAAiB,QAAQ,IAAI;;;;;;kDAC5C,8OAAC;wCAAG,WAAU;kDAAwC,QAAQ,KAAK;;;;;;kDACnE,8OAAC;wCAAE,WAAU;kDAAiC,QAAQ,WAAW;;;;;;;+BAR5D;;;;;;;;;;kCAcX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/PROJECTS/Bloopix%20MemeCoin/bloopix-website/src/components/Tokenomics.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\n\nexport default function Tokenomics() {\n  const [isVisible, setIsVisible] = useState(false);\n  const sectionRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.1 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const tokenomicsData = [\n    {\n      title: 'Supply',\n      value: 'Dynamic',\n      description: 'As per Pumpfun\\'s dynamic minting system',\n      icon: '📊',\n      color: 'from-blue-500 to-blue-600'\n    },\n    {\n      title: 'Pricing',\n      value: 'Bonding Curve',\n      description: 'Follows Pumpfun bonding curve - early buyers benefit',\n      icon: '📈',\n      color: 'from-purple-500 to-purple-600'\n    },\n    {\n      title: 'Distribution',\n      value: '100% Fair',\n      description: 'No pre-sale, no allocations. Everyone starts equal',\n      icon: '⚖️',\n      color: 'from-pink-500 to-pink-600'\n    },\n    {\n      title: 'Team Tokens',\n      value: '0%',\n      description: 'No team tokens reserved',\n      icon: '🚫',\n      color: 'from-green-500 to-green-600'\n    }\n  ];\n\n  const highlights = [\n    {\n      icon: '🏦',\n      title: 'No VC Backing',\n      description: 'Pure community-driven project with no venture capital involvement'\n    },\n    {\n      icon: '🎯',\n      title: 'No Utility Promises',\n      description: 'No overblown promises - just fun, community, and good vibes'\n    },\n    {\n      icon: '🌊',\n      title: 'Market Waves',\n      description: 'Designed to ride the natural waves of market sentiment'\n    }\n  ];\n\n  return (\n    <section id=\"tokenomics\" ref={sectionRef} className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n              Tokenomics\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Since Bloopix is developed on Pumpfun, it follows the platform&apos;s bonding curve mechanics\n              with complete transparency and fairness for all participants.\n            </p>\n          </div>\n\n          {/* Tokenomics Cards */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\">\n            {tokenomicsData.map((item, index) => (\n              <div\n                key={index}\n                className={`relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 ${\n                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n                }`}\n                style={{ transitionDelay: `${index * 100}ms` }}\n              >\n                <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${item.color}`}></div>\n                <div className=\"p-6\">\n                  <div className=\"text-4xl mb-4\">{item.icon}</div>\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{item.title}</h3>\n                  <div className={`text-2xl font-bold bg-gradient-to-r ${item.color} bg-clip-text text-transparent mb-3`}>\n                    {item.value}\n                  </div>\n                  <p className=\"text-gray-600 text-sm leading-relaxed\">{item.description}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Bonding Curve Explanation */}\n          <div className=\"bg-white rounded-2xl p-8 md:p-12 shadow-lg mb-16\">\n            <h3 className=\"text-3xl font-bold text-gray-900 mb-6 text-center\">\n              🎢 How the Bonding Curve Works\n            </h3>\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl\">1️⃣</span>\n                </div>\n                <h4 className=\"text-xl font-semibold mb-3\">Early Entry</h4>\n                <p className=\"text-gray-600\">Early buyers get the best prices as the curve starts low</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl\">2️⃣</span>\n                </div>\n                <h4 className=\"text-xl font-semibold mb-3\">Price Increases</h4>\n                <p className=\"text-gray-600\">As more people buy, the price gradually increases along the curve</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl\">3️⃣</span>\n                </div>\n                <h4 className=\"text-xl font-semibold mb-3\">Market Discovery</h4>\n                <p className=\"text-gray-600\">True market value is discovered through organic demand</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Key Highlights */}\n          <div className=\"grid md:grid-cols-3 gap-8 mb-12\">\n            {highlights.map((highlight, index) => (\n              <div\n                key={index}\n                className={`bg-gradient-to-br from-white to-gray-50 p-6 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300 ${\n                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n                }`}\n                style={{ transitionDelay: `${(index + 4) * 100}ms` }}\n              >\n                <div className=\"text-3xl mb-4\">{highlight.icon}</div>\n                <h4 className=\"text-xl font-bold text-gray-900 mb-3\">{highlight.title}</h4>\n                <p className=\"text-gray-600 leading-relaxed\">{highlight.description}</p>\n              </div>\n            ))}\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\">\n            <h3 className=\"text-3xl font-bold mb-4\">Ready to Make Waves? 🌊</h3>\n            <p className=\"text-xl mb-6 text-blue-100\">\n              Join the Bloopix community and be part of the most fun token on Pumpfun!\n            </p>\n            <a\n              href=\"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"inline-block bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg\"\n            >\n              🚀 Buy $BLP Now\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,WAAW,OAAO,EAAE;YACtB,SAAS,OAAO,CAAC,WAAW,OAAO;QACrC;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAa,KAAK;QAAY,WAAU;kBAClD,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,YAAY,8BAA8B,4BAA4B;;kCAEpH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;gCAEC,WAAW,CAAC,mIAAmI,EAC7I,YAAY,8BAA8B,4BAC1C;gCACF,OAAO;oCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gCAAC;;kDAE7C,8OAAC;wCAAI,WAAW,CAAC,mDAAmD,EAAE,KAAK,KAAK,EAAE;;;;;;kDAClF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAiB,KAAK,IAAI;;;;;;0DACzC,8OAAC;gDAAG,WAAU;0DAAyC,KAAK,KAAK;;;;;;0DACjE,8OAAC;gDAAI,WAAW,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,mCAAmC,CAAC;0DACnG,KAAK,KAAK;;;;;;0DAEb,8OAAC;gDAAE,WAAU;0DAAyC,KAAK,WAAW;;;;;;;;;;;;;+BAbnE;;;;;;;;;;kCAoBX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;0DAE7B,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;0DAE7B,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAW;;;;;;;;;;;0DAE7B,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;gCAEC,WAAW,CAAC,0HAA0H,EACpI,YAAY,8BAA8B,4BAC1C;gCACF,OAAO;oCAAE,iBAAiB,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC;gCAAC;;kDAEnD,8OAAC;wCAAI,WAAU;kDAAiB,UAAU,IAAI;;;;;;kDAC9C,8OAAC;wCAAG,WAAU;kDAAwC,UAAU,KAAK;;;;;;kDACrE,8OAAC;wCAAE,WAAU;kDAAiC,UAAU,WAAW;;;;;;;+BAR9D;;;;;;;;;;kCAcX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/PROJECTS/Bloopix%20MemeCoin/bloopix-website/src/components/Roadmap.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\n\nexport default function Roadmap() {\n  const [isVisible, setIsVisible] = useState(false);\n  const sectionRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.1 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  const roadmapPhases = [\n    {\n      phase: 'Phase 1',\n      title: 'Launch 🚀',\n      status: 'completed',\n      items: [\n        'Deploy on Pumpfun ✅',\n        'Share memes and graphics on social media ✅',\n        'Build a Telegram/X community ✅',\n        'Reach early holders and start the bonding curve climb ✅'\n      ],\n      color: 'from-green-500 to-green-600',\n      bgColor: 'from-green-50 to-green-100'\n    },\n    {\n      phase: 'Phase 2',\n      title: 'Community 👥',\n      status: 'in-progress',\n      items: [\n        'Meme contests and giveaways 🎯',\n        'Early supporter shout-outs 📢',\n        'Stickers, profile pictures, and other shareables 🎨',\n        'Community-driven content creation 🎭'\n      ],\n      color: 'from-blue-500 to-blue-600',\n      bgColor: 'from-blue-50 to-blue-100'\n    },\n    {\n      phase: 'Phase 3',\n      title: 'Splash Events 🌊',\n      status: 'upcoming',\n      items: [\n        'Possible NFT collaborations 🖼️',\n        'IRL or virtual community events 🎉',\n        'Partner with other meme projects for cross-promotions 🤝',\n        'Major exchange listings (if community demands) 📈'\n      ],\n      color: 'from-purple-500 to-purple-600',\n      bgColor: 'from-purple-50 to-purple-100'\n    }\n  ];\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return '✅';\n      case 'in-progress':\n        return '🔄';\n      case 'upcoming':\n        return '⏳';\n      default:\n        return '📋';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'Completed';\n      case 'in-progress':\n        return 'In Progress';\n      case 'upcoming':\n        return 'Upcoming';\n      default:\n        return 'Planned';\n    }\n  };\n\n  return (\n    <section id=\"roadmap\" ref={sectionRef} className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-6\">\n        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n              Roadmap\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Our journey is simple and transparent. No overblown promises, just organic growth \n              driven by community engagement and good vibes.\n            </p>\n          </div>\n\n          {/* Roadmap Timeline */}\n          <div className=\"relative\">\n            {/* Timeline Line */}\n            <div className=\"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-green-500 via-blue-500 to-purple-500 rounded-full hidden lg:block\"></div>\n\n            {/* Roadmap Items */}\n            <div className=\"space-y-12 lg:space-y-24\">\n              {roadmapPhases.map((phase, index) => (\n                <div\n                  key={index}\n                  className={`relative ${\n                    index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'\n                  } flex flex-col lg:flex items-center`}\n                >\n                  {/* Timeline Dot */}\n                  <div className=\"absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-white border-4 border-gray-300 rounded-full z-10 hidden lg:block\">\n                    <div className={`w-full h-full bg-gradient-to-r ${phase.color} rounded-full`}></div>\n                  </div>\n\n                  {/* Content Card */}\n                  <div\n                    className={`w-full lg:w-5/12 ${\n                      index % 2 === 0 ? 'lg:mr-auto lg:pr-12' : 'lg:ml-auto lg:pl-12'\n                    } ${\n                      isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'\n                    } transition-all duration-1000`}\n                    style={{ transitionDelay: `${index * 200}ms` }}\n                  >\n                    <div className={`bg-gradient-to-br ${phase.bgColor} rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2`}>\n                      {/* Phase Header */}\n                      <div className=\"flex items-center justify-between mb-6\">\n                        <div>\n                          <span className={`inline-block px-3 py-1 rounded-full text-sm font-semibold text-white bg-gradient-to-r ${phase.color} mb-2`}>\n                            {phase.phase}\n                          </span>\n                          <h3 className=\"text-2xl font-bold text-gray-900\">{phase.title}</h3>\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-2xl mb-1\">{getStatusIcon(phase.status)}</div>\n                          <span className={`text-sm font-medium ${\n                            phase.status === 'completed' ? 'text-green-600' :\n                            phase.status === 'in-progress' ? 'text-blue-600' :\n                            'text-purple-600'\n                          }`}>\n                            {getStatusText(phase.status)}\n                          </span>\n                        </div>\n                      </div>\n\n                      {/* Phase Items */}\n                      <ul className=\"space-y-3\">\n                        {phase.items.map((item, itemIndex) => (\n                          <li\n                            key={itemIndex}\n                            className=\"flex items-start space-x-3 text-gray-700\"\n                          >\n                            <span className=\"text-lg mt-0.5\">•</span>\n                            <span className=\"leading-relaxed\">{item}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Community Growth Section */}\n          <div className=\"mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-white\">\n            <h3 className=\"text-4xl font-bold mb-8 text-center\">Community & Growth 🌱</h3>\n            <p className=\"text-xl text-center mb-8 text-blue-100 max-w-4xl mx-auto\">\n              Bloopix thrives on memes, social engagement, and organic excitement. \n              Our growth comes from the community, not from artificial hype.\n            </p>\n            \n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">🎨</div>\n                <h4 className=\"font-semibold mb-2\">Viral Memes</h4>\n                <p className=\"text-blue-100 text-sm\">Creating and sharing viral memes and graphics</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">📱</div>\n                <h4 className=\"font-semibold mb-2\">Social Presence</h4>\n                <p className=\"text-blue-100 text-sm\">Active on X, Telegram, and other platforms</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">🤝</div>\n                <h4 className=\"font-semibold mb-2\">Collaborations</h4>\n                <p className=\"text-blue-100 text-sm\">Partnering with other fun tokens and projects</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-3\">🎁</div>\n                <h4 className=\"font-semibold mb-2\">Community Rewards</h4>\n                <p className=\"text-blue-100 text-sm\">Contests, giveaways, and exclusive content</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Disclaimer */}\n          <div className=\"mt-16 bg-gray-100 rounded-xl p-6 text-center\">\n            <h4 className=\"text-lg font-semibold text-gray-900 mb-3\">⚠️ Important Disclaimer</h4>\n            <p className=\"text-gray-600 text-sm leading-relaxed max-w-4xl mx-auto\">\n              Bloopix is a meme token launched purely for fun and community. It has no intrinsic value, \n              no promised utility, and no guarantee of profit. Anyone buying $BLP does so at their own risk \n              and should only participate if they&apos;re comfortable with the speculative and volatile nature of meme coins.\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,IAAI,WAAW,OAAO,EAAE;YACtB,SAAS,OAAO,CAAC,WAAW,OAAO;QACrC;QAEA,OAAO,IAAM,SAAS,UAAU;IAClC,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;YACD,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;YACD,OAAO;YACP,SAAS;QACX;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;YACD,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,KAAK;QAAY,WAAU;kBAC/C,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,YAAY,8BAA8B,4BAA4B;;kCAEpH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;wCAEC,WAAW,CAAC,SAAS,EACnB,QAAQ,MAAM,IAAI,gBAAgB,sBACnC,mCAAmC,CAAC;;0DAGrC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAW,CAAC,+BAA+B,EAAE,MAAM,KAAK,CAAC,aAAa,CAAC;;;;;;;;;;;0DAI9E,8OAAC;gDACC,WAAW,CAAC,iBAAiB,EAC3B,QAAQ,MAAM,IAAI,wBAAwB,sBAC3C,CAAC,EACA,YAAY,8BAA8B,2BAC3C,6BAA6B,CAAC;gDAC/B,OAAO;oDAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;gDAAC;0DAE7C,cAAA,8OAAC;oDAAI,WAAW,CAAC,kBAAkB,EAAE,MAAM,OAAO,CAAC,qGAAqG,CAAC;;sEAEvJ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAK,WAAW,CAAC,sFAAsF,EAAE,MAAM,KAAK,CAAC,KAAK,CAAC;sFACzH,MAAM,KAAK;;;;;;sFAEd,8OAAC;4EAAG,WAAU;sFAAoC,MAAM,KAAK;;;;;;;;;;;;8EAE/D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAiB,cAAc,MAAM,MAAM;;;;;;sFAC1D,8OAAC;4EAAK,WAAW,CAAC,oBAAoB,EACpC,MAAM,MAAM,KAAK,cAAc,mBAC/B,MAAM,MAAM,KAAK,gBAAgB,kBACjC,mBACA;sFACC,cAAc,MAAM,MAAM;;;;;;;;;;;;;;;;;;sEAMjC,8OAAC;4DAAG,WAAU;sEACX,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACtB,8OAAC;oEAEC,WAAU;;sFAEV,8OAAC;4EAAK,WAAU;sFAAiB;;;;;;sFACjC,8OAAC;4EAAK,WAAU;sFAAmB;;;;;;;mEAJ9B;;;;;;;;;;;;;;;;;;;;;;uCA5CV;;;;;;;;;;;;;;;;kCA4Db,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAE,WAAU;0CAA2D;;;;;;0CAKxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnF", "debugId": null}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/PROJECTS/Bloopix%20MemeCoin/bloopix-website/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\n\nexport default function Footer() {\n  const socialLinks = [\n    {\n      name: 'X (Twitter)',\n      url: 'https://x.com/bloopix11/status/1947960764603785465?s=46',\n      icon: '🐦',\n      handle: '@BLOOPIX11'\n    },\n    {\n      name: 'Telegram',\n      url: 'https://x.com/bloopix11/status/1947960764603785465?s=46',\n      icon: '💬',\n      handle: 'Join Community'\n    },\n    {\n      name: 'Pump.fun',\n      url: 'https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG',\n      icon: '🚀',\n      handle: 'Buy $BLP'\n    }\n  ];\n\n  const quickLinks = [\n    { name: 'About', href: '#about' },\n    { name: 'Tokenomics', href: '#tokenomics' },\n    { name: 'Roadmap', href: '#roadmap' },\n    { name: 'Whitepaper', href: '/whitepaper.pdf', external: true }\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white\">\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-6 py-16\">\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-6\">\n              <Image\n                src=\"/bloopix-logo.jpeg\"\n                alt=\"Bloopix Logo\"\n                width={60}\n                height={60}\n                className=\"rounded-full border-2 border-white/20\"\n              />\n              <div>\n                <h3 className=\"text-2xl font-bold\">Bloopix</h3>\n                <p className=\"text-blue-200\">$BLP</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 leading-relaxed mb-6 max-w-md\">\n              From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token \n              designed for degens who don&apos;t take themselves too seriously but know how to make waves.\n            </p>\n            \n            {/* Contract Address */}\n            <div className=\"bg-black/20 rounded-lg p-4 mb-6\">\n              <p className=\"text-sm text-gray-400 mb-2\">Contract Address:</p>\n              <div className=\"flex items-center space-x-2\">\n                <code className=\"text-sm font-mono text-blue-200 break-all\">\n                  9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG\n                </code>\n                <button\n                  onClick={() => navigator.clipboard.writeText('9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG')}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                  title=\"Copy to clipboard\"\n                >\n                  📋\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Quick Links</h4>\n            <ul className=\"space-y-3\">\n              {quickLinks.map((link, index) => (\n                <li key={index}>\n                  <a\n                    href={link.href}\n                    target={link.external ? '_blank' : undefined}\n                    rel={link.external ? 'noopener noreferrer' : undefined}\n                    className=\"text-gray-300 hover:text-white transition-colors\"\n                  >\n                    {link.name}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Social Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">Connect With Us</h4>\n            <div className=\"space-y-4\">\n              {socialLinks.map((social, index) => (\n                <a\n                  key={index}\n                  href={social.url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"flex items-center space-x-3 text-gray-300 hover:text-white transition-colors group\"\n                >\n                  <span className=\"text-xl group-hover:scale-110 transition-transform\">\n                    {social.icon}\n                  </span>\n                  <div>\n                    <div className=\"font-medium\">{social.name}</div>\n                    <div className=\"text-sm text-gray-400\">{social.handle}</div>\n                  </div>\n                </a>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"mt-12 pt-8 border-t border-white/10\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to Join the Wave? 🌊</h3>\n            <p className=\"text-gray-300 mb-6 max-w-2xl mx-auto\">\n              Don&apos;t miss out on the most fun token on Pumpfun. Join our community and let&apos;s make some waves together!\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105\"\n              >\n                🚀 Buy $BLP Now\n              </a>\n              <a\n                href=\"https://x.com/bloopix11/status/1947960764603785465?s=46\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-105\"\n              >\n                🐦 Follow on X\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-white/10 bg-black/20\">\n        <div className=\"max-w-7xl mx-auto px-6 py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2024 Bloopix. All rights reserved. Built with 💙 by the community.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm text-gray-400\">\n              <span>Made for fun, not financial advice</span>\n              <span>•</span>\n              <span>DYOR & Trade Responsibly</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Floating Bubbles */}\n      <div className=\"absolute inset-0 pointer-events-none overflow-hidden\">\n        {[...Array(10)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute w-2 h-2 bg-white/10 rounded-full animate-bounce\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 3}s`,\n              animationDuration: `${3 + Math.random() * 2}s`,\n            }}\n          />\n        ))}\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,cAAc;QAClB;YACE,MAAM;YACN,KAAK;YACL,MAAM;YACN,QAAQ;QACV;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM;YACN,QAAQ;QACV;QACA;YACE,MAAM;YACN,KAAK;YACL,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAc,MAAM;YAAmB,UAAU;QAAK;KAC/D;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAqB;;;;;;kEACnC,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAGjC,8OAAC;wCAAE,WAAU;kDAA8C;;;;;;kDAM3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAA4C;;;;;;kEAG5D,8OAAC;wDACC,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC;wDAC7C,WAAU;wDACV,OAAM;kEACP;;;;;;;;;;;;;;;;;;;;;;;;0CAQP,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;0DACC,cAAA,8OAAC;oDACC,MAAM,KAAK,IAAI;oDACf,QAAQ,KAAK,QAAQ,GAAG,WAAW;oDACnC,KAAK,KAAK,QAAQ,GAAG,wBAAwB;oDAC7C,WAAU;8DAET,KAAK,IAAI;;;;;;+CAPL;;;;;;;;;;;;;;;;0CAef,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC;gDAEC,MAAM,OAAO,GAAG;gDAChB,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;kEACb,OAAO,IAAI;;;;;;kEAEd,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAe,OAAO,IAAI;;;;;;0EACzC,8OAAC;gEAAI,WAAU;0EAAyB,OAAO,MAAM;;;;;;;;;;;;;+CAXlD;;;;;;;;;;;;;;;;;;;;;;kCAoBf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;8CAGpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;wBAEC,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC9B,gBAAgB,GAAG,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;4BACvC,mBAAmB,GAAG,IAAI,KAAK,MAAM,KAAK,EAAE,CAAC,CAAC;wBAChD;uBAPK;;;;;;;;;;;;;;;;AAajB", "debugId": null}}]}