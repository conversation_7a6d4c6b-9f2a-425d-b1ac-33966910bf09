{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/PROJECTS/Bloopix%20MemeCoin/bloopix-website/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\n\nexport const metadata: Metadata = {\n  title: \"Bloopix ($BLP) - The Fun Memecoin Making Waves on Pumpfun\",\n  description: \"From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token designed for degens who don't take themselves too seriously but know how to make waves.\",\n  keywords: \"Bloopix, BLP, memecoin, Pumpfun, cryptocurrency, meme token, community, degens\",\n  authors: [{ name: \"Bloopix Community\" }],\n  creator: \"Bloopix Community\",\n  publisher: \"Bloopix\",\n  openGraph: {\n    title: \"Bloop<PERSON> ($BLP) - The Fun Memecoin Making Waves\",\n    description: \"Join the most fun token on Pumpfun! Bloopix brings joy and community to the crypto space.\",\n    url: \"https://bloopix.fun\",\n    siteName: \"Bloopix\",\n    images: [\n      {\n        url: \"/bloopix-logo.jpeg\",\n        width: 1200,\n        height: 630,\n        alt: \"Bloopix Logo\",\n      },\n    ],\n    locale: \"en_US\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"<PERSON>loop<PERSON> ($BLP) - The Fun Memecoin Making Waves\",\n    description: \"Join the most fun token on Pumpfun! Bloopix brings joy and community to the crypto space.\",\n    creator: \"@BLOOPIX11\",\n    images: [\"/bloopix-logo.jpeg\"],\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <link rel=\"icon\" href=\"/bloopix-logo.jpeg\" />\n        <link rel=\"apple-touch-icon\" href=\"/bloopix-logo.jpeg\" />\n        <meta name=\"theme-color\" content=\"#3b82f6\" />\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n      </head>\n      <body className=\"antialiased\">\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAoB;KAAE;IACxC,SAAS;IACT,WAAW;IACX,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,SAAS;QACT,QAAQ;YAAC;SAAqB;IAChC;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAO,MAAK;;;;;;kCACtB,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAAW,SAAQ;;;;;;;;;;;;0BAEhC,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/PROJECTS/Bloopix%20MemeCoin/bloopix-website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}