(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},474:(a,b,c)=>{"use strict";c.d(b,{default:()=>e.a});var d=c(1261),e=c.n(d)},512:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return p},defaultHead:function(){return l}});let d=c(4985),e=c(740),f=c(687),g=e._(c(3210)),h=d._(c(7755)),i=c(4959),j=c(9513),k=c(4604);function l(a){void 0===a&&(a=!1);let b=[(0,f.jsx)("meta",{charSet:"utf-8"},"charset")];return a||b.push((0,f.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),b}function m(a,b){return"string"==typeof b||"number"==typeof b?a:b.type===g.default.Fragment?a.concat(g.default.Children.toArray(b.props.children).reduce((a,b)=>"string"==typeof b||"number"==typeof b?a:a.concat(b),[])):a.concat(b)}c(148);let n=["name","httpEquiv","charSet","itemProp"];function o(a,b){let{inAmpMode:c}=b;return a.reduce(m,[]).reverse().concat(l(c).reverse()).filter(function(){let a=new Set,b=new Set,c=new Set,d={};return e=>{let f=!0,g=!1;if(e.key&&"number"!=typeof e.key&&e.key.indexOf("$")>0){g=!0;let b=e.key.slice(e.key.indexOf("$")+1);a.has(b)?f=!1:a.add(b)}switch(e.type){case"title":case"base":b.has(e.type)?f=!1:b.add(e.type);break;case"meta":for(let a=0,b=n.length;a<b;a++){let b=n[a];if(e.props.hasOwnProperty(b))if("charSet"===b)c.has(b)?f=!1:c.add(b);else{let a=e.props[b],c=d[b]||new Set;("name"!==b||!g)&&c.has(a)?f=!1:(c.add(a),d[b]=c)}}}return f}}()).reverse().map((a,b)=>{let c=a.key||b;return g.default.cloneElement(a,{key:c})})}let p=function(a){let{children:b}=a,c=(0,g.useContext)(i.AmpStateContext),d=(0,g.useContext)(j.HeadManagerContext);return(0,f.jsx)(h.default,{reduceComponentsToState:o,headManager:d,inAmpMode:(0,k.isInAmpMode)(c),children:b})};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},587:()=>{},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1135:()=>{},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(7413),e=c(2554),f=c(9763),g=c(6732),h=c(2712),i=c(8659);function j(){return(0,d.jsxs)("main",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50",children:[(0,d.jsx)(e.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)(g.default,{}),(0,d.jsx)(h.default,{}),(0,d.jsx)(i.default,{})]})}},1261:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return i},getImageProps:function(){return h}});let d=c(4985),e=c(4953),f=c(6533),g=d._(c(1933));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},1317:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(687),e=c(474);function f(){return(0,d.jsxs)("footer",{className:"bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white",children:[(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-6 py-16",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,d.jsx)(e.default,{src:"/bloopix-logo.jpeg",alt:"Bloopix Logo",width:60,height:60,className:"rounded-full border-2 border-white/20"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-2xl font-bold",children:"Bloopix"}),(0,d.jsx)("p",{className:"text-blue-200",children:"$BLP"})]})]}),(0,d.jsx)("p",{className:"text-gray-300 leading-relaxed mb-6 max-w-md",children:"From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token designed for degens who don't take themselves too seriously but know how to make waves."}),(0,d.jsxs)("div",{className:"bg-black/20 rounded-lg p-4 mb-6",children:[(0,d.jsx)("p",{className:"text-sm text-gray-400 mb-2",children:"Contract Address:"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("code",{className:"text-sm font-mono text-blue-200 break-all",children:"9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG"}),(0,d.jsx)("button",{onClick:()=>navigator.clipboard.writeText("9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG"),className:"text-gray-400 hover:text-white transition-colors",title:"Copy to clipboard",children:"\uD83D\uDCCB"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Quick Links"}),(0,d.jsx)("ul",{className:"space-y-3",children:[{name:"About",href:"#about"},{name:"Tokenomics",href:"#tokenomics"},{name:"Roadmap",href:"#roadmap"},{name:"Whitepaper",href:"/whitepaper.pdf",external:!0}].map((a,b)=>(0,d.jsx)("li",{children:(0,d.jsx)("a",{href:a.href,target:a.external?"_blank":void 0,rel:a.external?"noopener noreferrer":void 0,className:"text-gray-300 hover:text-white transition-colors",children:a.name})},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-lg font-semibold mb-6",children:"Connect With Us"}),(0,d.jsx)("div",{className:"space-y-4",children:[{name:"X (Twitter)",url:"https://x.com/bloopix11/status/1947960764603785465?s=46",icon:"\uD83D\uDC26",handle:"@BLOOPIX11"},{name:"Telegram",url:"https://x.com/bloopix11/status/1947960764603785465?s=46",icon:"\uD83D\uDCAC",handle:"Join Community"},{name:"Pump.fun",url:"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG",icon:"\uD83D\uDE80",handle:"Buy $BLP"}].map((a,b)=>(0,d.jsxs)("a",{href:a.url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-3 text-gray-300 hover:text-white transition-colors group",children:[(0,d.jsx)("span",{className:"text-xl group-hover:scale-110 transition-transform",children:a.icon}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-gray-400",children:a.handle})]})]},b))})]})]}),(0,d.jsx)("div",{className:"mt-12 pt-8 border-t border-white/10",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Ready to Join the Wave? \uD83C\uDF0A"}),(0,d.jsx)("p",{className:"text-gray-300 mb-6 max-w-2xl mx-auto",children:"Don't miss out on the most fun token on Pumpfun. Join our community and let's make some waves together!"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)("a",{href:"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG",target:"_blank",rel:"noopener noreferrer",className:"bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105",children:"\uD83D\uDE80 Buy $BLP Now"}),(0,d.jsx)("a",{href:"https://x.com/bloopix11/status/1947960764603785465?s=46",target:"_blank",rel:"noopener noreferrer",className:"border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-105",children:"\uD83D\uDC26 Follow on X"})]})]})})]}),(0,d.jsx)("div",{className:"border-t border-white/10 bg-black/20",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-6",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,d.jsx)("div",{className:"text-gray-400 text-sm",children:"\xa9 2024 Bloopix. All rights reserved. Built with \uD83D\uDC99 by the community."}),(0,d.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-400",children:[(0,d.jsx)("span",{children:"Made for fun, not financial advice"}),(0,d.jsx)("span",{children:"•"}),(0,d.jsx)("span",{children:"DYOR & Trade Responsibly"})]})]})})}),(0,d.jsx)("div",{className:"absolute inset-0 pointer-events-none overflow-hidden",children:[...Array(10)].map((a,b)=>(0,d.jsx)("div",{className:"absolute w-2 h-2 bg-white/10 rounded-full animate-bounce",style:{left:`${100*Math.random()}%`,top:`${100*Math.random()}%`,animationDelay:`${3*Math.random()}s`,animationDuration:`${3+2*Math.random()}s`}},b))})]})}},1480:(a,b)=>{"use strict";function c(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImageBlurSvg",{enumerable:!0,get:function(){return c}})},1933:(a,b)=>{"use strict";function c(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return d}}),c.__next_img_default=!0;let d=c},2554:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Hero.tsx","default")},2660:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/app/page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/app/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},2712:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Roadmap.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Roadmap.tsx","default")},2756:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{VALID_LOADERS:function(){return c},imageConfigDefault:function(){return d}});let c=["default","imgix","cloudinary","akamai","custom"],d={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},2937:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(687),e=c(3210);function f(){let[a,b]=(0,e.useState)(!1),c=(0,e.useRef)(null);return(0,d.jsx)("section",{id:"about",ref:c,className:"py-20 bg-white",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-6",children:(0,d.jsxs)("div",{className:`transition-all duration-1000 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:"About Bloopix"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Bloopix is here to remind everyone that crypto doesn't always have to be serious. Amid the charts, candles, and anxiety-inducing volatility, Bloopix splashes onto the scene to deliver joy and a light-hearted reminder of what brought many of us here."})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 mb-20",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-2xl",children:[(0,d.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"\uD83C\uDFAF Our Vision"}),(0,d.jsx)("p",{className:"text-gray-700 text-lg leading-relaxed",children:"Bloopix is here to remind everyone that crypto doesn't always have to be serious. Amid the charts, candles, and anxiety-inducing volatility, Bloopix splashes onto the scene to deliver joy and a light-hearted reminder of what brought many of us here in the first place: curiosity, community, and a little chaos."})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-br from-purple-50 to-pink-50 p-8 rounded-2xl",children:[(0,d.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"\uD83D\uDE80 Our Mission"}),(0,d.jsxs)("ul",{className:"text-gray-700 text-lg space-y-3",children:[(0,d.jsx)("li",{children:"• Inject humor and positivity into the Pumpfun scene"}),(0,d.jsx)("li",{children:"• Build a vibrant, self-aware community of degens and dreamers"}),(0,d.jsx)("li",{children:"• Ride the waves of Pumpfun while creating memorable experiences"}),(0,d.jsx)("li",{children:"• Show that fun coins can still make big splashes"})]})]})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:[{icon:"\uD83C\uDF0A",title:"Community-Driven",description:"Built by degens, for degens. No VCs, no team tokens, just pure community vibes."},{icon:"\uD83C\uDFAF",title:"Fair Launch",description:"100% fair launch on Pumpfun. Everyone starts at the same price, no pre-sales or allocations."},{icon:"\uD83D\uDE80",title:"Meme Power",description:"Powered by memes, viral content, and the collective energy of the Bloopix community."},{icon:"\uD83D\uDC8E",title:"Diamond Hands",description:"For holders who believe in the power of fun, community, and making waves in crypto."}].map((b,c)=>(0,d.jsxs)("div",{className:`bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${100*c}ms`},children:[(0,d.jsx)("div",{className:"text-4xl mb-4",children:b.icon}),(0,d.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:b.title}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:b.description})]},c))}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-white",children:[(0,d.jsx)("h3",{className:"text-4xl font-bold mb-8 text-center",children:"Why Bloopix? \uD83E\uDD14"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDE0A"}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Cute & Relatable"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"A happy, bubbly character designed to resonate with everyone"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-3",children:"\uD83E\uDDE0"}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Memorable Branding"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"Easy to recognize and share across all platforms"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDC65"}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Community-Oriented"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"What matters is the people who hold it, share it, and spread the joy"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDE0C"}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"No Pressure"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"No overblown roadmap or unrealistic promises - just good vibes"})]})]})]})]})})})}},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=c(3210);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3492:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(687),e=c(474),f=c(3210);function g(){let[a,b]=(0,f.useState)(!1);return(0,d.jsxs)("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 opacity-90",children:(0,d.jsx)("div",{className:"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%224%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] animate-pulse"})}),(0,d.jsx)("nav",{className:"absolute top-0 left-0 right-0 z-50 p-6",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)(e.default,{src:"/bloopix-logo.jpeg",alt:"Bloopix Logo",width:50,height:50,className:"rounded-full border-2 border-white/20"}),(0,d.jsx)("span",{className:"text-white text-2xl font-bold",children:"Bloopix"})]}),(0,d.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[(0,d.jsx)("a",{href:"#about",className:"text-white/90 hover:text-white transition-colors",children:"About"}),(0,d.jsx)("a",{href:"#tokenomics",className:"text-white/90 hover:text-white transition-colors",children:"Tokenomics"}),(0,d.jsx)("a",{href:"#roadmap",className:"text-white/90 hover:text-white transition-colors",children:"Roadmap"}),(0,d.jsx)("a",{href:"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG",target:"_blank",rel:"noopener noreferrer",className:"bg-white text-purple-600 px-6 py-2 rounded-full font-semibold hover:bg-gray-100 transition-colors",children:"Buy $BLP"})]})]})}),(0,d.jsx)("div",{className:"relative z-10 text-center px-6 max-w-5xl mx-auto",children:(0,d.jsxs)("div",{className:`transition-all duration-1000 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)(e.default,{src:"/bloopix-logo.jpeg",alt:"Bloopix Logo",width:200,height:200,className:"mx-auto rounded-full border-4 border-white/30 shadow-2xl"})}),(0,d.jsxs)("h1",{className:"text-6xl md:text-8xl font-bold text-white mb-6 leading-tight",children:["Bloopix",(0,d.jsx)("span",{className:"block text-4xl md:text-5xl text-blue-200 font-normal mt-2",children:"$BLP"})]}),(0,d.jsx)("p",{className:"text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed",children:"From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token designed for degens who don't take themselves too seriously but know how to make waves."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,d.jsx)("a",{href:"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG",target:"_blank",rel:"noopener noreferrer",className:"bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg",children:"\uD83D\uDE80 Buy $BLP on Pump.fun"}),(0,d.jsx)("a",{href:"https://x.com/bloopix11/status/1947960764603785465?s=46",target:"_blank",rel:"noopener noreferrer",className:"border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-105",children:"\uD83D\uDC26 Follow on X"})]}),(0,d.jsxs)("div",{className:"mt-12 text-white/80",children:[(0,d.jsx)("p",{className:"text-sm mb-2",children:"Contract Address:"}),(0,d.jsx)("code",{className:"bg-black/20 px-4 py-2 rounded-lg text-sm font-mono break-all",children:"9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG"})]})]})}),(0,d.jsx)("div",{className:"absolute inset-0 pointer-events-none",children:[...Array(20)].map((a,b)=>(0,d.jsx)("div",{className:"absolute w-4 h-4 bg-white/20 rounded-full animate-bounce",style:{left:`${100*Math.random()}%`,top:`${100*Math.random()}%`,animationDelay:`${2*Math.random()}s`,animationDuration:`${2+2*Math.random()}s`}},b))}),(0,d.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce",children:(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("span",{className:"text-sm mb-2",children:"Scroll to explore"}),(0,d.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})]})})]})}},3873:a=>{"use strict";a.exports=require("path")},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(7413);c(1135);let e={title:"Bloopix ($BLP) - The Fun Memecoin Making Waves on Pumpfun",description:"From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token designed for degens who don't take themselves too seriously but know how to make waves.",keywords:"Bloopix, BLP, memecoin, Pumpfun, cryptocurrency, meme token, community, degens",authors:[{name:"Bloopix Community"}],creator:"Bloopix Community",publisher:"Bloopix",openGraph:{title:"Bloopix ($BLP) - The Fun Memecoin Making Waves",description:"Join the most fun token on Pumpfun! Bloopix brings joy and community to the crypto space.",url:"https://bloopix.fun",siteName:"Bloopix",images:[{url:"/bloopix-logo.jpeg",width:1200,height:630,alt:"Bloopix Logo"}],locale:"en_US",type:"website"},twitter:{card:"summary_large_image",title:"Bloopix ($BLP) - The Fun Memecoin Making Waves",description:"Join the most fun token on Pumpfun! Bloopix brings joy and community to the crypto space.",creator:"@BLOOPIX11",images:["/bloopix-logo.jpeg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function f({children:a}){return(0,d.jsxs)("html",{lang:"en",children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("link",{rel:"icon",href:"/bloopix-logo.jpeg"}),(0,d.jsx)("link",{rel:"apple-touch-icon",href:"/bloopix-logo.jpeg"}),(0,d.jsx)("meta",{name:"theme-color",content:"#3b82f6"}),(0,d.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"})]}),(0,d.jsx)("body",{className:"antialiased",children:a})]})}},4604:(a,b)=>{"use strict";function c(a){let{ampFirst:b=!1,hybrid:c=!1,hasQuery:d=!1}=void 0===a?{}:a;return b||c&&d}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isInAmpMode",{enumerable:!0,get:function(){return c}})},4686:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(687),e=c(3210);function f(){let[a,b]=(0,e.useState)(!1),c=(0,e.useRef)(null);return(0,d.jsx)("section",{id:"roadmap",ref:c,className:"py-20 bg-white",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-6",children:(0,d.jsxs)("div",{className:`transition-all duration-1000 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:"Roadmap"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Our journey is simple and transparent. No overblown promises, just organic growth driven by community engagement and good vibes."})]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-green-500 via-blue-500 to-purple-500 rounded-full hidden lg:block"}),(0,d.jsx)("div",{className:"space-y-12 lg:space-y-24",children:[{phase:"Phase 1",title:"Launch \uD83D\uDE80",status:"completed",items:["Deploy on Pumpfun ✅","Share memes and graphics on social media ✅","Build a Telegram/X community ✅","Reach early holders and start the bonding curve climb ✅"],color:"from-green-500 to-green-600",bgColor:"from-green-50 to-green-100"},{phase:"Phase 2",title:"Community \uD83D\uDC65",status:"in-progress",items:["Meme contests and giveaways \uD83C\uDFAF","Early supporter shout-outs \uD83D\uDCE2","Stickers, profile pictures, and other shareables \uD83C\uDFA8","Community-driven content creation \uD83C\uDFAD"],color:"from-blue-500 to-blue-600",bgColor:"from-blue-50 to-blue-100"},{phase:"Phase 3",title:"Splash Events \uD83C\uDF0A",status:"upcoming",items:["Possible NFT collaborations \uD83D\uDDBC️","IRL or virtual community events \uD83C\uDF89","Partner with other meme projects for cross-promotions \uD83E\uDD1D","Major exchange listings (if community demands) \uD83D\uDCC8"],color:"from-purple-500 to-purple-600",bgColor:"from-purple-50 to-purple-100"}].map((b,c)=>(0,d.jsxs)("div",{className:`relative ${c%2==0?"lg:flex-row":"lg:flex-row-reverse"} flex flex-col lg:flex items-center`,children:[(0,d.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-white border-4 border-gray-300 rounded-full z-10 hidden lg:block",children:(0,d.jsx)("div",{className:`w-full h-full bg-gradient-to-r ${b.color} rounded-full`})}),(0,d.jsx)("div",{className:`w-full lg:w-5/12 ${c%2==0?"lg:mr-auto lg:pr-12":"lg:ml-auto lg:pl-12"} ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-10"} transition-all duration-1000`,style:{transitionDelay:`${200*c}ms`},children:(0,d.jsxs)("div",{className:`bg-gradient-to-br ${b.bgColor} rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:`inline-block px-3 py-1 rounded-full text-sm font-semibold text-white bg-gradient-to-r ${b.color} mb-2`,children:b.phase}),(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:b.title})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("div",{className:"text-2xl mb-1",children:(a=>{switch(a){case"completed":return"✅";case"in-progress":return"\uD83D\uDD04";case"upcoming":return"⏳";default:return"\uD83D\uDCCB"}})(b.status)}),(0,d.jsx)("span",{className:`text-sm font-medium ${"completed"===b.status?"text-green-600":"in-progress"===b.status?"text-blue-600":"text-purple-600"}`,children:(a=>{switch(a){case"completed":return"Completed";case"in-progress":return"In Progress";case"upcoming":return"Upcoming";default:return"Planned"}})(b.status)})]})]}),(0,d.jsx)("ul",{className:"space-y-3",children:b.items.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start space-x-3 text-gray-700",children:[(0,d.jsx)("span",{className:"text-lg mt-0.5",children:"•"}),(0,d.jsx)("span",{className:"leading-relaxed",children:a})]},b))})]})})]},c))})]}),(0,d.jsxs)("div",{className:"mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-white",children:[(0,d.jsx)("h3",{className:"text-4xl font-bold mb-8 text-center",children:"Community & Growth \uD83C\uDF31"}),(0,d.jsx)("p",{className:"text-xl text-center mb-8 text-blue-100 max-w-4xl mx-auto",children:"Bloopix thrives on memes, social engagement, and organic excitement. Our growth comes from the community, not from artificial hype."}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDFA8"}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Viral Memes"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"Creating and sharing viral memes and graphics"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-3",children:"\uD83D\uDCF1"}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Social Presence"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"Active on X, Telegram, and other platforms"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-3",children:"\uD83E\uDD1D"}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Collaborations"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"Partnering with other fun tokens and projects"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-3",children:"\uD83C\uDF81"}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Community Rewards"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"Contests, giveaways, and exclusive content"})]})]})]}),(0,d.jsxs)("div",{className:"mt-16 bg-gray-100 rounded-xl p-6 text-center",children:[(0,d.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-3",children:"⚠️ Important Disclaimer"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed max-w-4xl mx-auto",children:"Bloopix is a meme token launched purely for fun and community. It has no intrinsic value, no promised utility, and no guarantee of profit. Anyone buying $BLP does so at their own risk and should only participate if they're comfortable with the speculative and volatile nature of meme coins."})]})]})})})}},4931:()=>{},4953:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"getImgProps",{enumerable:!0,get:function(){return i}}),c(148);let d=c(1480),e=c(2756),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},4959:(a,b,c)=>{"use strict";a.exports=c(4041).vendored.contexts.AmpContext},6375:(a,b,c)=>{Promise.resolve().then(c.bind(c,2937)),Promise.resolve().then(c.bind(c,1317)),Promise.resolve().then(c.bind(c,3492)),Promise.resolve().then(c.bind(c,4686)),Promise.resolve().then(c.bind(c,6678))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6533:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"Image",{enumerable:!0,get:function(){return u}});let d=c(4985),e=c(740),f=c(687),g=e._(c(3210)),h=d._(c(1215)),i=d._(c(512)),j=c(4953),k=c(2756),l=c(7903);c(148);let m=c(9148),n=d._(c(1933)),o=c(3038),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function q(a,b,c,d,e,f,g){let h=null==a?void 0:a.src;a&&a["data-loaded-src"]!==h&&(a["data-loaded-src"]=h,("decode"in a?a.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(a.parentElement&&a.isConnected){if("empty"!==b&&e(!0),null==c?void 0:c.current){let b=new Event("load");Object.defineProperty(b,"target",{writable:!1,value:a});let d=!1,e=!1;c.current({...b,nativeEvent:b,currentTarget:a,target:a,isDefaultPrevented:()=>d,isPropagationStopped:()=>e,persist:()=>{},preventDefault:()=>{d=!0,b.preventDefault()},stopPropagation:()=>{e=!0,b.stopPropagation()}})}(null==d?void 0:d.current)&&d.current(a)}}))}function r(a){return g.use?{fetchPriority:a}:{fetchpriority:a}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let s=(0,g.forwardRef)((a,b)=>{let{src:c,srcSet:d,sizes:e,height:h,width:i,decoding:j,className:k,style:l,fetchPriority:m,placeholder:n,loading:p,unoptimized:s,fill:t,onLoadRef:u,onLoadingCompleteRef:v,setBlurComplete:w,setShowAltText:x,sizesInput:y,onLoad:z,onError:A,...B}=a,C=(0,g.useCallback)(a=>{a&&(A&&(a.src=a.src),a.complete&&q(a,n,u,v,w,s,y))},[c,n,u,v,w,A,s,y]),D=(0,o.useMergedRef)(b,C);return(0,f.jsx)("img",{...B,...r(m),loading:p,width:i,height:h,decoding:j,"data-nimg":t?"fill":"1",className:k,style:l,sizes:e,srcSet:d,src:c,ref:D,onLoad:a=>{q(a.currentTarget,n,u,v,w,s,y)},onError:a=>{x(!0),"empty"!==n&&w(!0),A&&A(a)}})});function t(a){let{isAppRouter:b,imgAttributes:c}=a,d={as:"image",imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:c.crossOrigin,referrerPolicy:c.referrerPolicy,...r(c.fetchPriority)};return b&&h.default.preload?(h.default.preload(c.src,d),null):(0,f.jsx)(i.default,{children:(0,f.jsx)("link",{rel:"preload",href:c.srcSet?void 0:c.src,...d},"__nimg-"+c.src+c.srcSet+c.sizes)})}let u=(0,g.forwardRef)((a,b)=>{let c=(0,g.useContext)(m.RouterContext),d=(0,g.useContext)(l.ImageConfigContext),e=(0,g.useMemo)(()=>{var a;let b=p||d||k.imageConfigDefault,c=[...b.deviceSizes,...b.imageSizes].sort((a,b)=>a-b),e=b.deviceSizes.sort((a,b)=>a-b),f=null==(a=b.qualities)?void 0:a.sort((a,b)=>a-b);return{...b,allSizes:c,deviceSizes:e,qualities:f}},[d]),{onLoad:h,onLoadingComplete:i}=a,o=(0,g.useRef)(h);(0,g.useEffect)(()=>{o.current=h},[h]);let q=(0,g.useRef)(i);(0,g.useEffect)(()=>{q.current=i},[i]);let[r,u]=(0,g.useState)(!1),[v,w]=(0,g.useState)(!1),{props:x,meta:y}=(0,j.getImgProps)(a,{defaultLoader:n.default,imgConf:e,blurComplete:r,showAltText:v});return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(s,{...x,unoptimized:y.unoptimized,placeholder:y.placeholder,fill:y.fill,onLoadRef:o,onLoadingCompleteRef:q,setBlurComplete:u,setShowAltText:w,sizesInput:a.sizes,ref:b}),y.priority?(0,f.jsx)(t,{isAppRouter:!c,imgAttributes:x}):null]})});("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6678:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(687),e=c(3210);function f(){let[a,b]=(0,e.useState)(!1),c=(0,e.useRef)(null);return(0,d.jsx)("section",{id:"tokenomics",ref:c,className:"py-20 bg-gradient-to-br from-gray-50 to-blue-50",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-6",children:(0,d.jsxs)("div",{className:`transition-all duration-1000 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[(0,d.jsxs)("div",{className:"text-center mb-16",children:[(0,d.jsx)("h2",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:"Tokenomics"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Since Bloopix is developed on Pumpfun, it follows the platform's bonding curve mechanics with complete transparency and fairness for all participants."})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16",children:[{title:"Supply",value:"Dynamic",description:"As per Pumpfun's dynamic minting system",icon:"\uD83D\uDCCA",color:"from-blue-500 to-blue-600"},{title:"Pricing",value:"Bonding Curve",description:"Follows Pumpfun bonding curve - early buyers benefit",icon:"\uD83D\uDCC8",color:"from-purple-500 to-purple-600"},{title:"Distribution",value:"100% Fair",description:"No pre-sale, no allocations. Everyone starts equal",icon:"⚖️",color:"from-pink-500 to-pink-600"},{title:"Team Tokens",value:"0%",description:"No team tokens reserved",icon:"\uD83D\uDEAB",color:"from-green-500 to-green-600"}].map((b,c)=>(0,d.jsxs)("div",{className:`relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${100*c}ms`},children:[(0,d.jsx)("div",{className:`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${b.color}`}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"text-4xl mb-4",children:b.icon}),(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:b.title}),(0,d.jsx)("div",{className:`text-2xl font-bold bg-gradient-to-r ${b.color} bg-clip-text text-transparent mb-3`,children:b.value}),(0,d.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:b.description})]})]},c))}),(0,d.jsxs)("div",{className:"bg-white rounded-2xl p-8 md:p-12 shadow-lg mb-16",children:[(0,d.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-6 text-center",children:"\uD83C\uDFA2 How the Bonding Curve Works"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("span",{className:"text-2xl",children:"1️⃣"})}),(0,d.jsx)("h4",{className:"text-xl font-semibold mb-3",children:"Early Entry"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Early buyers get the best prices as the curve starts low"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("span",{className:"text-2xl",children:"2️⃣"})}),(0,d.jsx)("h4",{className:"text-xl font-semibold mb-3",children:"Price Increases"}),(0,d.jsx)("p",{className:"text-gray-600",children:"As more people buy, the price gradually increases along the curve"})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)("span",{className:"text-2xl",children:"3️⃣"})}),(0,d.jsx)("h4",{className:"text-xl font-semibold mb-3",children:"Market Discovery"}),(0,d.jsx)("p",{className:"text-gray-600",children:"True market value is discovered through organic demand"})]})]})]}),(0,d.jsx)("div",{className:"grid md:grid-cols-3 gap-8 mb-12",children:[{icon:"\uD83C\uDFE6",title:"No VC Backing",description:"Pure community-driven project with no venture capital involvement"},{icon:"\uD83C\uDFAF",title:"No Utility Promises",description:"No overblown promises - just fun, community, and good vibes"},{icon:"\uD83C\uDF0A",title:"Market Waves",description:"Designed to ride the natural waves of market sentiment"}].map((b,c)=>(0,d.jsxs)("div",{className:`bg-gradient-to-br from-white to-gray-50 p-6 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,style:{transitionDelay:`${(c+4)*100}ms`},children:[(0,d.jsx)("div",{className:"text-3xl mb-4",children:b.icon}),(0,d.jsx)("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:b.title}),(0,d.jsx)("p",{className:"text-gray-600 leading-relaxed",children:b.description})]},c))}),(0,d.jsxs)("div",{className:"text-center bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white",children:[(0,d.jsx)("h3",{className:"text-3xl font-bold mb-4",children:"Ready to Make Waves? \uD83C\uDF0A"}),(0,d.jsx)("p",{className:"text-xl mb-6 text-blue-100",children:"Join the Bloopix community and be part of the most fun token on Pumpfun!"}),(0,d.jsx)("a",{href:"https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG",target:"_blank",rel:"noopener noreferrer",className:"inline-block bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg",children:"\uD83D\uDE80 Buy $BLP Now"})]})]})})})}},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6732:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Tokenomics.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Tokenomics.tsx","default")},7755:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return f}});let d=c(3210),e=()=>{};function f(a){var b;let{headManager:c,reduceComponentsToState:f}=a;function g(){if(c&&c.mountedInstances){let b=d.Children.toArray(Array.from(c.mountedInstances).filter(Boolean));c.updateHead(f(b,a))}}return null==c||null==(b=c.mountedInstances)||b.add(a.children),g(),e(()=>{var b;return null==c||null==(b=c.mountedInstances)||b.add(a.children),()=>{var b;null==c||null==(b=c.mountedInstances)||b.delete(a.children)}}),e(()=>(c&&(c._pendingUpdate=g),()=>{c&&(c._pendingUpdate=g)})),null}},7903:(a,b,c)=>{"use strict";a.exports=c(4041).vendored.contexts.ImageConfigContext},8127:(a,b,c)=>{Promise.resolve().then(c.bind(c,9763)),Promise.resolve().then(c.bind(c,8659)),Promise.resolve().then(c.bind(c,2554)),Promise.resolve().then(c.bind(c,2712)),Promise.resolve().then(c.bind(c,6732))},8354:a=>{"use strict";a.exports=require("util")},8373:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},8659:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Footer.tsx","default")},8869:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(a,b,c)=>{"use strict";a.exports=c(4041).vendored.contexts.RouterContext},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9513:(a,b,c)=>{"use strict";a.exports=c(4041).vendored.contexts.HeadManagerContext},9763:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/About.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/About.tsx","default")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,24],()=>b(b.s=2660));module.exports=c})();