[{"/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/app/layout.tsx": "1", "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/app/page.tsx": "2", "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/About.tsx": "3", "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Footer.tsx": "4", "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Hero.tsx": "5", "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Roadmap.tsx": "6", "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Tokenomics.tsx": "7"}, {"size": 1983, "mtime": 1753310448380, "results": "8", "hashOfConfig": "9"}, {"size": 457, "mtime": 1753310143570, "results": "10", "hashOfConfig": "9"}, {"size": 6012, "mtime": 1753310602387, "results": "11", "hashOfConfig": "9"}, {"size": 7002, "mtime": 1753310655177, "results": "12", "hashOfConfig": "9"}, {"size": 5728, "mtime": 1753310623397, "results": "13", "hashOfConfig": "9"}, {"size": 9070, "mtime": 1753310677962, "results": "14", "hashOfConfig": "9"}, {"size": 7251, "mtime": 1753310706365, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pi8f37", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/app/layout.tsx", [], [], "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/app/page.tsx", [], [], "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/About.tsx", [], [], "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Footer.tsx", [], [], "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Hero.tsx", [], [], "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Roadmap.tsx", [], [], "/home/<USER>/PROJECTS/Bloopix MemeCoin/bloopix-website/src/components/Tokenomics.tsx", [], []]