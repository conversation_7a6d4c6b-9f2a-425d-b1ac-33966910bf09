import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Bloopix ($BLP) - The Fun Memecoin Making Waves on Pumpfun",
  description: "From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token designed for degens who don't take themselves too seriously but know how to make waves.",
  keywords: "Bloopix, BLP, memecoin, Pumpfun, cryptocurrency, meme token, community, degens",
  authors: [{ name: "Bloopix Community" }],
  creator: "Bloopix Community",
  publisher: "Bloopix",
  openGraph: {
    title: "Bloop<PERSON> ($BLP) - The Fun Memecoin Making Waves",
    description: "Join the most fun token on Pumpfun! Bloopix brings joy and community to the crypto space.",
    url: "https://bloopix.fun",
    siteName: "Bloopix",
    images: [
      {
        url: "/bloopix-logo.jpeg",
        width: 1200,
        height: 630,
        alt: "Bloopix Logo",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "<PERSON>loop<PERSON> ($BLP) - The Fun Memecoin Making Waves",
    description: "Join the most fun token on Pumpfun! Bloopix brings joy and community to the crypto space.",
    creator: "@BLOOPIX11",
    images: ["/bloopix-logo.jpeg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/bloopix-logo.jpeg" />
        <link rel="apple-touch-icon" href="/bloopix-logo.jpeg" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
