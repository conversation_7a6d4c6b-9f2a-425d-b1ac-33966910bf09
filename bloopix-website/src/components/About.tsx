'use client';

import { useState, useEffect, useRef } from 'react';

export default function About() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const features = [
    {
      icon: '🌊',
      title: 'Community-Driven',
      description: 'Built by degens, for degens. No VCs, no team tokens, just pure community vibes.'
    },
    {
      icon: '🎯',
      title: 'Fair Launch',
      description: '100% fair launch on Pumpfun. Everyone starts at the same price, no pre-sales or allocations.'
    },
    {
      icon: '🚀',
      title: 'Meme Power',
      description: 'Powered by memes, viral content, and the collective energy of the Bloopix community.'
    },
    {
      icon: '💎',
      title: 'Diamond Hands',
      description: 'For holders who believe in the power of fun, community, and making waves in crypto.'
    }
  ];

  return (
    <section id="about" ref={sectionRef} className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              About Bloopix
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Bloopix is here to remind everyone that crypto doesn&apos;t always have to be serious.
              Amid the charts, candles, and anxiety-inducing volatility, Bloopix splashes onto 
              the scene to deliver joy and a light-hearted reminder of what brought many of us here.
            </p>
          </div>

          {/* Vision & Mission */}
          <div className="grid md:grid-cols-2 gap-12 mb-20">
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-2xl">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">🎯 Our Vision</h3>
              <p className="text-gray-700 text-lg leading-relaxed">
                Bloopix is here to remind everyone that crypto doesn&apos;t always have to be serious.
                Amid the charts, candles, and anxiety-inducing volatility, Bloopix splashes onto 
                the scene to deliver joy and a light-hearted reminder of what brought many of us 
                here in the first place: curiosity, community, and a little chaos.
              </p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-8 rounded-2xl">
              <h3 className="text-3xl font-bold text-gray-900 mb-4">🚀 Our Mission</h3>
              <ul className="text-gray-700 text-lg space-y-3">
                <li>• Inject humor and positivity into the Pumpfun scene</li>
                <li>• Build a vibrant, self-aware community of degens and dreamers</li>
                <li>• Ride the waves of Pumpfun while creating memorable experiences</li>
                <li>• Show that fun coins can still make big splashes</li>
              </ul>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`bg-white p-6 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h4>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>

          {/* Why Bloopix */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-4xl font-bold mb-8 text-center">Why Bloopix? 🤔</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-3xl mb-3">😊</div>
                <h4 className="font-semibold mb-2">Cute & Relatable</h4>
                <p className="text-blue-100 text-sm">A happy, bubbly character designed to resonate with everyone</p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-3">🧠</div>
                <h4 className="font-semibold mb-2">Memorable Branding</h4>
                <p className="text-blue-100 text-sm">Easy to recognize and share across all platforms</p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-3">👥</div>
                <h4 className="font-semibold mb-2">Community-Oriented</h4>
                <p className="text-blue-100 text-sm">What matters is the people who hold it, share it, and spread the joy</p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-3">😌</div>
                <h4 className="font-semibold mb-2">No Pressure</h4>
                <p className="text-blue-100 text-sm">No overblown roadmap or unrealistic promises - just good vibes</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
