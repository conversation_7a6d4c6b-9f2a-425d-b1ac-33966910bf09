'use client';

import Image from 'next/image';

export default function Footer() {
  const socialLinks = [
    {
      name: 'X (Twitter)',
      url: 'https://x.com/bloopix11/status/1947960764603785465?s=46',
      icon: '🐦',
      handle: '@BLOOPIX11'
    },
    {
      name: 'Telegram',
      url: 'https://x.com/bloopix11/status/1947960764603785465?s=46',
      icon: '💬',
      handle: 'Join Community'
    },
    {
      name: 'Pump.fun',
      url: 'https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG',
      icon: '🚀',
      handle: 'Buy $BLP'
    }
  ];

  const quickLinks = [
    { name: 'About', href: '#about' },
    { name: 'Tokenomics', href: '#tokenomics' },
    { name: 'Roadmap', href: '#roadmap' },
    { name: 'Whitepaper', href: '/whitepaper.pdf', external: true }
  ];

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-6 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <Image
                src="/bloopix-logo.jpeg"
                alt="Bloopix Logo"
                width={60}
                height={60}
                className="rounded-full border-2 border-white/20"
              />
              <div>
                <h3 className="text-2xl font-bold">Bloopix</h3>
                <p className="text-blue-200">$BLP</p>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed mb-6 max-w-md">
              From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token 
              designed for degens who don&apos;t take themselves too seriously but know how to make waves.
            </p>
            
            {/* Contract Address */}
            <div className="bg-black/20 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-400 mb-2">Contract Address:</p>
              <div className="flex items-center space-x-2">
                <code className="text-sm font-mono text-blue-200 break-all">
                  9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG
                </code>
                <button
                  onClick={() => navigator.clipboard.writeText('9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG')}
                  className="text-gray-400 hover:text-white transition-colors"
                  title="Copy to clipboard"
                >
                  📋
                </button>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    target={link.external ? '_blank' : undefined}
                    rel={link.external ? 'noopener noreferrer' : undefined}
                    className="text-gray-300 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Social Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Connect With Us</h4>
            <div className="space-y-4">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 text-gray-300 hover:text-white transition-colors group"
                >
                  <span className="text-xl group-hover:scale-110 transition-transform">
                    {social.icon}
                  </span>
                  <div>
                    <div className="font-medium">{social.name}</div>
                    <div className="text-sm text-gray-400">{social.handle}</div>
                  </div>
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-12 pt-8 border-t border-white/10">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Ready to Join the Wave? 🌊</h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Don&apos;t miss out on the most fun token on Pumpfun. Join our community and let&apos;s make some waves together!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-purple-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
              >
                🚀 Buy $BLP Now
              </a>
              <a
                href="https://x.com/bloopix11/status/1947960764603785465?s=46"
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-105"
              >
                🐦 Follow on X
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-white/10 bg-black/20">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © 2024 Bloopix. All rights reserved. Built with 💙 by the community.
            </div>
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <span>Made for fun, not financial advice</span>
              <span>•</span>
              <span>DYOR & Trade Responsibly</span>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Bubbles */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {[...Array(10)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/10 rounded-full animate-bounce"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>
    </footer>
  );
}
