'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';

export default function Hero() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Animation */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 opacity-90">
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%224%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] animate-pulse"></div>
      </div>

      {/* Navigation */}
      <nav className="absolute top-0 left-0 right-0 z-50 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Image
              src="/bloopix-logo.jpeg"
              alt="Bloopix Logo"
              width={50}
              height={50}
              className="rounded-full border-2 border-white/20"
            />
            <span className="text-white text-2xl font-bold">Bloopix</span>
          </div>
          
          <div className="hidden md:flex items-center space-x-8">
            <a href="#about" className="text-white/90 hover:text-white transition-colors">About</a>
            <a href="#tokenomics" className="text-white/90 hover:text-white transition-colors">Tokenomics</a>
            <a href="#roadmap" className="text-white/90 hover:text-white transition-colors">Roadmap</a>
            <a 
              href="https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-purple-600 px-6 py-2 rounded-full font-semibold hover:bg-gray-100 transition-colors"
            >
              Buy $BLP
            </a>
          </div>
        </div>
      </nav>

      {/* Hero Content */}
      <div className="relative z-10 text-center px-6 max-w-5xl mx-auto">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          <div className="mb-8">
            <Image
              src="/bloopix-logo.jpeg"
              alt="Bloopix Logo"
              width={200}
              height={200}
              className="mx-auto rounded-full border-4 border-white/30 shadow-2xl"
            />
          </div>
          
          <h1 className="text-6xl md:text-8xl font-bold text-white mb-6 leading-tight">
            Bloopix
            <span className="block text-4xl md:text-5xl text-blue-200 font-normal mt-2">
              $BLP
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
            From the depths of the blockchain ocean comes Bloopix, a bubbly, unpredictable token 
            designed for degens who don&apos;t take themselves too seriously but know how to make waves.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a
              href="https://pump.fun/9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-white text-purple-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              🚀 Buy $BLP on Pump.fun
            </a>
            
            <a
              href="https://x.com/bloopix11/status/1947960764603785465?s=46"
              target="_blank"
              rel="noopener noreferrer"
              className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-105"
            >
              🐦 Follow on X
            </a>
          </div>
          
          <div className="mt-12 text-white/80">
            <p className="text-sm mb-2">Contract Address:</p>
            <code className="bg-black/20 px-4 py-2 rounded-lg text-sm font-mono break-all">
              9wtoaKGG9BHq19kuvQ5LNj9PCLKCourVAbkTe3HpTBYG
            </code>
          </div>
        </div>
      </div>

      {/* Floating Bubbles Animation */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-4 h-4 bg-white/20 rounded-full animate-bounce"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
        <div className="flex flex-col items-center">
          <span className="text-sm mb-2">Scroll to explore</span>
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  );
}
